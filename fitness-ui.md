# Fitness Chat Socket.IO API Documentation

## Overview
This document outlines the Socket.IO API for the fitness chat functionality, which enables direct peer-to-peer messaging between users in the fitness module.

## Connection Setup

### Initial Connection
```javascript
const socket = io('your-socket-server-url', {
  path: '/chat/socket.io',
  query: {
    userId: 'user123',
    chatType: 'fitness'  // IMPORTANT: Set this to enable fitness chat mode
  }
});
```

### Connection Parameters
- `userId` (required): Unique identifier for the connecting user
- `chatType` (required): Must be set to `'fitness'` to enable fitness chat functionality
- Server will default to 'batayeq' if not specified

## Events You Can Emit (Client → Server)

### 1. Get Members
Retrieve all users who have had conversations with the current user.

```javascript
socket.emit('getMembers', {
  // No additional parameters required for fitness mode
});
```

**Response Event**: `membersData`

### 2. Read Messages
Get conversation history between two users.

```javascript
socket.emit('readMessages', {
  receiverId: 'user456',  // ID of the other user in conversation
  limit: 20,              // Number of messages to retrieve
  offset: 0               // Pagination offset
});
```

**Response Event**: `readMessages`

### 3. Send Message
Send a message to another user.

```javascript
socket.emit('sendMessage', {
  receiverId: 'user456',  // ID of the recipient
  message: 'Hello there!' // Message content
});
```

**Response Events**: 
- `messageSent` (confirmation to sender)
- `newMessage` (delivered to recipient if online)

## Events You Can Listen To (Server → Client)

### 1. Members Data
Received after emitting `getMembers`.

```javascript
socket.on('membersData', (members) => {
  console.log('Chat members:', members);
  // Array of users who have chatted with current user
});
```

### 2. Read Messages Response
Received after emitting `readMessages`.

```javascript
socket.on('readMessages', (messages) => {
  console.log('Conversation history:', messages);
  // Array of messages between two users
});
```

### 3. New Message
Received when another user sends you a message.

```javascript
socket.on('newMessage', (message) => {
  console.log('New message received:', message);
  // Display the new message in UI
});
```

### 4. Message Sent Confirmation
Received after successfully sending a message.

```javascript
socket.on('messageSent', (message) => {
  console.log('Message sent successfully:', message);
  // Update UI to show message as sent
});
```

### 5. Online Users
Received when users come online or go offline.

```javascript
socket.on('getOnlineUsers', (userIds) => {
  console.log('Currently online users:', userIds);
  // Array of user IDs who are currently online
});
```

### 6. Online Status Updates
Received when a specific user's status changes.

```javascript
socket.on('onlineStatus', (data) => {
  console.log('User status changed:', data);
  // { userId: 'user123', status: true/false }
});
```

### 7. Fitness User Offline
Received when a fitness chat user goes offline.

```javascript
socket.on('fitnessUserOffline', (data) => {
  console.log('Fitness user went offline:', data);
  // { userId: 'user123', timestamp: Date }
});
```

### 8. Error Handling
Listen for errors from the server.

```javascript
socket.on('error', (errorMessage) => {
  console.error('Socket error:', errorMessage);
  // Handle error appropriately in UI
});
```

## Key Differences from Batayeq Chat

| Feature | Fitness Chat | Batayeq Chat |
|---------|-------------|--------------|
| Chat Type | Direct peer-to-peer messaging | Group-based messaging |
| Room Concept | No rooms/groups | Uses subOrderNumber as rooms |
| Message Routing | Direct to specific user's socket | Broadcast to room members |
| Last Seen | Updates automatically on disconnect | Updates on leaveGroup event |
| Member List | Users who have chatted with you | Order-based members |

## Implementation Example

```javascript
class FitnessChat {
  constructor(userId) {
    this.socket = io('your-server-url', {
      path: '/chat/socket.io',
      query: {
        userId: userId,
        chatType: 'fitness'
      }
    });
    
    this.setupEventListeners();
  }
  
  setupEventListeners() {
    // Handle incoming messages
    this.socket.on('newMessage', (message) => {
      this.displayMessage(message);
    });
    
    // Handle members list
    this.socket.on('membersData', (members) => {
      this.updateMembersList(members);
    });
    
    // Handle message history
    this.socket.on('readMessages', (messages) => {
      this.displayMessageHistory(messages);
    });
    
    // Handle online status
    this.socket.on('onlineStatus', (data) => {
      this.updateUserStatus(data.userId, data.status);
    });
  }
  
  // Send a message
  sendMessage(receiverId, messageContent) {
    this.socket.emit('sendMessage', {
      receiverId: receiverId,
      message: messageContent
    });
  }
  
  // Load conversation with a user
  loadConversation(receiverId, limit = 20, offset = 0) {
    this.socket.emit('readMessages', {
      receiverId: receiverId,
      limit: limit,
      offset: offset
    });
  }
  
  // Get list of chat members
  getMembers() {
    this.socket.emit('getMembers', {});
  }
}
```

## Error Scenarios

The server will emit an `error` event in these cases:
- Missing `userId` in connection query
- Any database operation failures
- Invalid data in emit events

Always implement error handling:

```javascript
socket.on('error', (errorMessage) => {
  // Show user-friendly error message
  showNotification('error', errorMessage);
});
```

## Notes for UI Development

1. **Connection State**: Always check if the socket is connected before emitting events
2. **Message Status**: Use `messageSent` event to update UI with delivery confirmation
3. **Online Indicators**: Use `onlineStatus` and `getOnlineUsers` to show online/offline status
4. **Real-time Updates**: All message events happen in real-time, no polling needed
5. **Pagination**: Use `limit` and `offset` for message history pagination
6. **Auto-reconnection**: Socket.IO handles reconnection automatically, but you may want to refresh data on reconnection

## Testing Connection

```javascript
// Test if fitness chat mode is working
socket.on('connect', () => {
  console.log('Connected to fitness chat');
  socket.emit('getMembers', {});
});
```