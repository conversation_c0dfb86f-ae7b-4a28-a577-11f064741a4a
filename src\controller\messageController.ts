import ApiResponse from '../helpers/response';
import messageDal from '../dal/messageDal';
import SocketHelper from '../helpers/socketHelper';
import { connection } from 'mongoose';

export default class MessageController {
    public messageDal: messageDal;

    constructor(socketHelper: SocketHelper) {
        this.messageDal = new messageDal(socketHelper);
    }

    public createMessage = async (req: any) => {
        try {
            if (req.body.chatType === 'fitness') {
                let result = await this.messageDal.sendFitnessMessage(
                    req.params.id,
                    req.body.receiverId,
                    req.body.message
                );
                return new ApiResponse(result.code, false, result.message, result.data);
            } else {
                let result = await this.messageDal.createMessage(req);
                return new ApiResponse(result.code, false, result.message, result.data);
            }


        } catch (error: any) {
            return new ApiResponse(500, false, 'internal-server-error', `${error.message}`);
        }
    };

    public sendNotification = async (req: any) => {
        try {
            let result = await this.messageDal.sendNotification(req);
            if (!result.status) {
                return new ApiResponse(result.code, false, result.message, result.data);
            }
            return new ApiResponse(result.code, true, result.message, result.data);
        } catch (error: any) {
            return new ApiResponse(500, false, 'internal-server-error', `${error.message}`);
        }
    };

    public getConversation = async (req: any) => {
        try {


            if (req.query.chatType === 'fitness') {
                req.senderId = req.params.id
                req.receiverId = req.query.receiverId
                let result = await this.messageDal.getFitnessConversation(req);
                if (!result.status) {
                    return new ApiResponse(result.code, false, result.message, result.data);
                }
                return new ApiResponse(result.code, false, result.message, result.data);
            } else {
                let result = await this.messageDal.getConversation(req);
                if (!result.status) {
                    return new ApiResponse(result.code, false, result.message, result.data);
                }
                return new ApiResponse(result.code, true, result.message, result.data);
            }

        } catch (error: any) {
            return new ApiResponse(500, false, 'internal-server-error', `${error.message}`);
        }
    };

    public getMembers = async (req: any) => {
        try {
            let type = req.query.type;
            let result: any;

            if (type === "user") {
                result = await this.messageDal.getMembersforUser(req);
            } else if (type === "seller") {
                result = await this.messageDal.getMembersforSeller(req);
            } else {
                result = await this.messageDal.adminOrderMember(req);
            }
            if (!result.status) {
                return new ApiResponse(result.code, false, result.message, result.data);
            }
            return new ApiResponse(result.code, true, result.message, result.data);
        } catch (error: any) {
            return new ApiResponse(500, false, 'internal-server-error', `${error.message}`);
        }
    };
}