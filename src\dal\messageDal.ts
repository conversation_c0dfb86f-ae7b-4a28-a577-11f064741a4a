import SocketHelper from '../helpers/socketHelper';
// import { connection } from '../db/connection';
import { connection } from 'mongoose';
import { Types } from 'mongoose';
import { notificationModel } from '../models/notificationModel';
import {messageModel} from '../models/messageModel';
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import Applicationservice from '../services/applicationService';
import messageHelper from '../helpers/messageHelper';
import { Upload } from "@aws-sdk/lib-storage";
import * as  AWS from "@aws-sdk/client-s3";
import ApiResponse from "../helpers/response";


export default class messageDal {
  private applicationService: Applicationservice;
  // private app: any;
  public SocketHelper: SocketHelper;
  private messageHelper: messageHelper;

  constructor(socketHelper: SocketHelper) {
    this.applicationService = new Applicationservice();
    // this.app = this.app;
    this.SocketHelper = socketHelper; // Use the passed socketHelper instance
    this.messageHelper = new messageHelper();
  }

  public createMessage = async (req: any): Promise<any> => {
    try {
        if (!req.params.id) {
            return { code: 400, status: false, message: `senderId is required`, data: null };
        }

        if (!req.body.subOrderNumber) {
            return { code: 400, status: false, message: `subOrderNumber is required`, data: null };
        }

        if (!req.body.type || (req.body.type !== "user" && req.body.type !== "seller" && req.body.type !== "admin")) {
            return { code: 400, status: false, message: `type is invalid or not found`, data: null };
        }

        const sender = req.params.id;
        const message = req.body.message;
        const subOrderNumber = req.body.subOrderNumber;
        const type = req.body.type;
        let images = [];
        let savedMessages = [];

      //   if (req.files && req.files.length > 0) {
      //     images = req.files.map((file: any) => ({
      //         fileName: file.originalname,
      //         image: file.buffer.toString('base64'),
      //         type: file.mimetype
      //     }));
      // }
if(req.files && req.files.length > 0) {
  for (const file of req.files) {
    let V4 = new Date().getTime();
    let keyName = `chat/${V4}${file.originalname}`;
    
    const s3Config: any = {
      credentials: {
        accessKeyId: process.env.ACCESSKEYID,
        secretAccessKey: process.env.SECRETACCESSKEY,
      },
      region: process.env.REGION,
    };

    const client = new AWS.S3Client(s3Config);

    const params = {
      Bucket: process.env.BUCKET_NAME,
      Key: keyName,
      Body: file.buffer,
      ContentType: file.mimetype
    };

    const resultFile = await new Upload({ client, params }).done();
    

    if (resultFile['$metadata'].httpStatusCode !== 200) {
      return new ApiResponse(400, false, "Upload failed or was aborted.");
    }

    const messageBody = {
      chatType: 'batayeq', // Default to batayeq for existing functionality
      subOrderNumber,
      sender: new Types.ObjectId(sender),
      message: '',
      image: {
          fileName: file.originalname,
          image: keyName,
          type: file.mimetype
      },
      role: req.body.type,
      createdAt: new Date()
  };

  const newMessage = new messageModel(messageBody);
  const result = await newMessage.save();
  savedMessages.push(result);

      
  }
}
        const userModel: any = connection.db?.collection('users');
        // const senderDataPromise = userModel.aggregate()
        //     .match({ _id: new Types.ObjectId(sender) })
        //     .project({ _id: 1, displayName: 1, role: 1 })
        //     .toArray();

        // const messageBody = {
        //     subOrderNumber: subOrderNumber,
        //     sender: new Types.ObjectId(sender),
        //     message: message,
        //     image: images,
        //     createdAt: new Date()
        // };

        // const newMessage = new messageModel(messageBody);
        // const result = await newMessage.save();
        if (req.body.message) {
          const messageBody = {
              chatType: 'batayeq', // Default to batayeq for existing functionality
              subOrderNumber:subOrderNumber,
              sender: new Types.ObjectId(sender),
              message: req.body.message,
              image: {},
              role: req.body.type,
              createdAt: new Date()
          };

          const newMessage = new messageModel(messageBody);
          let result = await newMessage.save();
          console.log("result", result);
          savedMessages.push(result);
      }
        if (savedMessages.length > 0) {
            // const senderData = await senderDataPromise;
            // messageBody.sender = senderData[0];
            const io = this.SocketHelper.io;
            console.log("llll,", savedMessages);
            for (const message of savedMessages) {
              console.log("message", message);
              io.to(subOrderNumber).emit('receiveMessage', message);
          }

            // Emit an event to update the pendingMessages count for the specific user
            // io.to(subOrderNumber).emit('receiveMessage', messageBody);

            const room = io.sockets.adapter.rooms.get(subOrderNumber);
            const roomMembers = room ? Array.from(room) : [];
            
            const allMemberSet: any = new Set([sender]);

            await this.updateMessageForMember(sender, subOrderNumber, type);

            const memberPromises = roomMembers.map(async (socketId) => {
                if (socketId) {
                    const existingMember: any = this.SocketHelper.getUserIdBySocketId(socketId);
                    allMemberSet.add(existingMember);
                }
            });

            await Promise.all(memberPromises);

            // for chat reloadings
            const adminPromise = userModel.findOne({ role: "admin" });
            let otherUser: any;

            if (type === "user") {
                otherUser = await this.findSellerId(subOrderNumber, sender);
                allMemberSet.add(otherUser);
            } else if (type === "seller") {
                otherUser = await this.findUserId(subOrderNumber, sender);
                allMemberSet.add(otherUser);
            } else if (type === "admin") {
                const otherUsers = await this.getSellerAndUserId(subOrderNumber);
                allMemberSet.add(otherUsers?.sellerId.toString());
                allMemberSet.add(otherUsers?.userId.toString());
            }

            const admin = await adminPromise;
            allMemberSet.add(admin?._id.toString());
       
            const reloadPromises = Array.from(allMemberSet).map(async (member) => {
                const socketId = await this.SocketHelper.getReceiverSocketId(member);
                if (socketId) {
                  console.log("socketId for ",member," is : ",socketId,"loaded for chatCOunt")
                    io.to(socketId).emit('reloadChat', { userId:member });
                }
            });

            await Promise.all(reloadPromises);

            return { status: true, code: 200, message: "Data saved successfully", data: savedMessages };
        }

        return { status: false, code: 409, message: `failed to save data`, data: [] };
    } catch (error: any) {
        return { code: 500, status: false, message: `Database-Error`, data: error.message };
    }
}

public sendNotification = async (req: any): Promise<any> => {
  try {

    let userid = req.params.userid;

    if (!userid) {
      return { code: 400, status: false, message: `userid is required`, data: null };
    }
    let payload = {
      userid,
      type: req.body.type,
      role: req.body.role,
      content: req.body.content,
      // isRead: false

    }
    const notification = new notificationModel(payload);
    const result = await notification.save();

    if (result) {
      const io = this.SocketHelper.io; // Use the existing io instance

      const socketId = await this.SocketHelper.getReceiverSocketId(userid);
      if (socketId) {

          io.to(socketId).emit('reloadNotofication', { result });
      }

      return { status: true, code: 200, message: `Data saved successfully`, data: result };
    }
    return { status: false, code: 409, message: `failed to save data`, data: result };
  } catch (error: any) {
      return { code: 500, status: false, message: `Database-Error`, data: error.message };
  }
}

  private isUserInGroup = async (groupName: string, userId: string): Promise<any> => {
  
    const io = this.SocketHelper.socketSetup();


    const room = io.sockets.adapter.rooms.get(groupName);
    const roomMembers = room ? Array.from(room) : [];

    if (roomMembers.length === 0) {
      return false;
    }
    
    let ActiveMap: any = []

    roomMembers.map(async (socketId) => {
      if (socketId) {
        const existingMember: any = this.SocketHelper.getUserIdBySocketId(socketId);

          ActiveMap.push(existingMember)
      }
    });

    let inGroup = ActiveMap.includes(userId);

    return inGroup;
  };

  
public getNotificationsByUserId = async (req: any): Promise<any> => {
  try {
    let userid = req.params.userid;
    let limit = parseInt(req.query.limit) || 10;
    let offset = parseInt(req.query.offset) || 0;

    if (!userid) {
      return { code: 400, status: false, message: `userid is required`, data: null };
    }

    const notifications = await notificationModel.find({ userid: new Types.ObjectId(userid) })
      .sort({ createdAt: -1 }) // Sort by createdAt in descending order
      .skip(offset)
      .limit(limit)
      .exec();

    if (notifications.length > 0) {
      return { status: true, code: 200, message: `Notifications retrieved successfully`, data: notifications };
    } else {
      return { status: false, code: 404, message: `No notifications found for the given userid`, data: null };
    }
  } catch (error: any) {
    return { code: 500, status: false, message: `Database-Error`, data: error.message };
  }
}

public updateUserOnline = async (req: any): Promise<any> => {
  try {
    let userid = req.params.userid;

    if (!userid) {
      return { code: 400, status: false, message: `userid is required`, data: null };
    }
    const userModel: any = connection.db?.collection('users');

    const updateResult = await userModel.updateOne(
      { _id: new Types.ObjectId(userid) },
      { $set: { userOnline: new Date() } }
    );

    if (updateResult.modifiedCount > 0) {
      return { status: true, code: 200, message: `User online status updated successfully`, data: updateResult };
    } else {
      return { status: false, code: 404, message: `No user found for the given userid`, data: null };
    }
  } catch (error: any) {
    return { code: 500, status: false, message: `Database-Error`, data: error.message };
  }
}

public getUnreadNotificationsCount = async (req: any): Promise<any> => {
  try {
    let userid = req.params.userid;

    if (!userid) {
      return { code: 400, status: false, message: `userid is required`, data: null };
    }

    const userModel: any = connection.db?.collection('users');


    // Fetch the userOnline timestamp from the users collection
    const user = await userModel.findById(userid).select('userOnline').exec();

    if (!user || !user.userOnline) {
      return { code: 404, status: false, message: 'User or userOnline timestamp not found', data: null };
    }

    const userOnline = user.userOnline;

    // Count the number of notifications where createdAt is greater than userOnline
    const unreadNotificationsCount = await notificationModel.countDocuments({
      userid: new Types.ObjectId(userid),
      createdAt: { $gt: userOnline }
    });

    return { status: true, code: 200, message: 'Unread notifications count retrieved successfully', data: unreadNotificationsCount };
  } catch (error: any) {
    return { code: 500, status: false, message: 'Database-Error', data: error.message };
  }
}
  public getConversation = async (req: any): Promise<any> => {
    try {

      if (!req.params.id) {
        return { code: 400, status: false, message: `senderId is required`, data: null };
      }

      let userId= req.query.userId

      let limit = parseInt(req.query.limit) || 10;
      let offset = parseInt(req.query.offset) || 0;
      // let search = req.query.search || '';

      let conversation = await messageModel.aggregate([
        {
          $match: {
              subOrderNumber: req.params.id,
          }
        },
        {
          $lookup: {
            from: 'users',
            localField: 'sender',
            foreignField: '_id',
            as: 'sender'
          }

        },

        {
          $facet: {
            paginatedResults: [
              { $sort: { "createdAt": -1 } },
              { $skip: offset },
              { $limit: limit },
              {
                $project: {
                  "_id": 1,
                  "message": 1,
                  "image": 1,
                  "role" : 1,
                  "displayName" : { $arrayElemAt: ["$sender.displayName", 0] },
                  "firstName" : { $arrayElemAt: ["$sender.firstName", 0] },
                  "createdAt": 1,
                  "isSender": { $eq: [{ $arrayElemAt: ["$sender._id", 0] }, new Types.ObjectId(userId)] }
                }
              }
            ],
            totalCount: [
              { $count: "count" }
            ]
          }
        }

      ]);
      // let total = await messageModel.countDocuments({subOrderNumber: req.params.id})
      if(conversation[0].paginatedResults.length > 0){
       for(let i = 0; i < conversation[0].paginatedResults.length; i++){
        if(conversation[0].paginatedResults[i].image){
          conversation[0].paginatedResults[i].image.signedUrl = await this.getSignedUrl(conversation[0].paginatedResults[i].image.image)
        
      }
       }
          }
      let totalData = {
        conversation : conversation[0].paginatedResults || [],
        fetchCount : conversation[0].paginatedResults.length || 0,
        total : conversation[0].totalCount[0] ? conversation[0].totalCount[0].count : 0
      }
      return { status: true, code: 200, message: `Data retrieved successfully`, data: totalData };
    } catch (error: any) {
      return { code: 500, status: false, message: `Database-Error`, data: error.message };
    }
  }

public getSignedUrl = async (image: any): Promise<any> => {
  try {
    
    const s3Config: any = {
      region: process.env.REGION,
      credentials: {
        accessKeyId: process.env.ACCESSKEYID,
        secretAccessKey: process.env.SECRETACCESSKEY,
      },
    };

    const client = new AWS.S3Client(s3Config);

    try {
      // Check if the file exists in S3 using a `HEAD` request
      const headCommand = new AWS.HeadObjectCommand({
        Bucket: process.env.BUCKET_NAME,
        Key: image,
      });
      
      await client.send(headCommand); // Throws if file does not exist

      // File exists, proceed to generate signed URL
      const getObjectCommand = new AWS.GetObjectCommand({
        Bucket: process.env.BUCKET_NAME,
        Key: image,
      });

      const signedUrl = await getSignedUrl(client, getObjectCommand);

      return signedUrl;
     
    } catch (error: any) {
      // Handle the case where the file is missing in S3
      if (error.name === "NoSuchKey" || error.name === "NotFound") {
        return new ApiResponse(
          404,
          false,
          "File not found in S3 bucket. It may have been deleted."
        );
      }
      
      // Handle any other errors
      throw error;
    }
  } catch (error: any) {
    return { code: 500, status: false, message: `Database-Error`, data: error.message };
  }
}
public getMembersforUser = async (req: any) => {
  try {

    let search = await this.messageHelper.search(req.query)

    let Id: string = req.params.id
    let limit = parseInt(req.query.limit) || 10;
    let offset = parseInt(req.query.offset) || 0;


    let orders: any = connection.db?.collection('orders')
    let result = await orders.aggregate([
      {
        $match: { userId: new Types.ObjectId(Id) }
      },
      {
        $unwind: "$sellerData"
      },
      {
        $lookup: {
          from: 'messages',
          localField: 'sellerData.subOrderNumber',
          foreignField: 'subOrderNumber',
          as: 'messages'
        }
      },
      {
        $match: { 'messages.0': { $exists: true } } // Filter to only include subOrderNumbers that have messages
      },
      {
        $addFields: {
          lastMessage: { $arrayElemAt: ["$messages", -1] } // Get the last message for each subOrderNumber
        }
      },
      {
        $group: {
          _id: "$sellerData.subOrderNumber",
          sellerId: { $first: "$sellerData.sellerId" },
          lastMessage: { $first: "$lastMessage" }
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'sellerId',
          foreignField: '_id',
          as: 'sellerData'
        }
      },
      {
        $unwind: "$sellerData"
      },
      {
        $project: {
          _id: 0,
          subOrderNumber: "$_id",
          user: {
            _id: "$sellerData._id",
            userName: "$sellerData.userName",
            displayName: "$sellerData.displayName",
            role: "$sellerData.role",
            profilePictureId: "$sellerData.profilePictureId",
          },
          lastMessage: {
            message: "$lastMessage.message",
            createdAt: "$lastMessage.createdAt"
          }
        }
      },
      { $match: { $or: search } },
      { $sort: { "lastMessage.createdAt": -1 } },
      { $skip: offset },
      { $limit: limit }
    ]).toArray();

    if (result.length > 0) {
      for (const item of result) {
        if (item.user.profilePictureId) {
          const newId: any = await this.applicationService.getFile(req, item.user.profilePictureId.toString());
          item.user.profilePicture = newId.data.signedUrl; // Replace the original _id with the result of the async operation
        }
        
        let count
        // let inGroup = await this.isUserInGroup(item.subOrderNumber, Id);
        
        // if(inGroup){
        //   count = 0
        // }else{
          count = await this.getPendingMessages(Id, item.subOrderNumber, "user");
          item["pendingMessages"] = count.data;
        // }
       
      }
    }

    return { status: true, code: 200, message: `Data retrieved successfully`, data: result };

  } catch (error: any) {
    return { code: 500, status: false, message: `Database-Error`, data: error.message };
  }
}

public async findUser(userId:  string) {
  if (!connection.db) {
    throw new Error("Database connection is not established.");
  }
  let userModel = connection.db.collection('users');
  const userData = await userModel.findOne({
    userId: new Types.ObjectId(userId),
  }, { projection: { 'firstName': 1 ,'lastName':1,'middleName':1,"displayName":1} });

  return userData  ? userData: null;
}

public getMembersforSeller = async (req: any) => {
  try {

    let search = await this.messageHelper.search(req.query)

    let sellerId: string = req.params.id;
    let limit = parseInt(req.query.limit) || 10;
    let offset = parseInt(req.query.offset) || 0;

    let orders: any = connection.db?.collection('orders');
    let result = await orders.aggregate([
      {
        $match: { 'sellerData.sellerId': new Types.ObjectId(sellerId) }
      },
      {
        $unwind: "$sellerData"
      },
      {
        $lookup: {
          from: 'messages',
          localField: 'sellerData.subOrderNumber',
          foreignField: 'subOrderNumber',
          as: 'messages'
        }
      },
      {
        $match: { 'messages.0': { $exists: true } } // Filter to only include subOrderNumbers that have messages
      },
      {
        $addFields: {
          lastMessage: { $arrayElemAt: ["$messages", -1] } // Get the last message for each subOrderNumber
        }
      },
      {
        $group: {
          _id: "$sellerData.subOrderNumber",
          userId: { $first: "$userId" },
          lastMessage: { $first: "$lastMessage" }
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'userId',
          foreignField: '_id',
          as: 'userData'
        }
      },
      {
        $unwind: "$userData"
      },
      {
        $project: {
          _id: 0,
          subOrderNumber: "$_id",
          user: {
            _id: "$userData._id",
            firstName: "$userData.firstName",
            middleName: "$userData.middleName",
            lastName: "$userData.lastName",
            role: "$userData.role",
            profilePictureId: "$userData.profilePictureId"
          },
          lastMessage: {
            message: "$lastMessage.message",
            createdAt: "$lastMessage.createdAt"
          }
        }
      },
      { $match: { $or: search } },
      { $sort: { "lastMessage.createdAt": -1 } },
      {
        $skip: offset
      },
      {
        $limit: limit
      }
    ]).toArray();
    if (result.length > 0) {
      for (const item of result) {
        if (item.user.profilePictureId) {
          const newId: any = await this.applicationService.getFile(req, item.user.profilePictureId.toString());
          item.user.profilePicture = newId.data.signedUrl; // Replace the original _id with the result of the async operation
        }
        
        // let count = await this.getPendingMessages(sellerId, item.subOrderNumber, "seller");

        console.log("userSocketMap",this.SocketHelper.userSocketMap)
        console.log("user item",item.user._id)
        let active = Object.keys(this.SocketHelper.userSocketMap).includes(item.user._id.toString());

        item["status"] = active;

        // item["pendingMessages"] = count.data;

        let count
        // let inGroup = await this.isUserInGroup(item.subOrderNumber, sellerId);
        
        // if(inGroup){
        //   count = 0
        // }else{
          count = await this.getPendingMessages(sellerId, item.subOrderNumber, "seller");
          item["pendingMessages"] = count.data;
        // }
      }
    }
    return { status: true, code: 200, message: `Data retrieved successfully`, data: result };

  } catch (error: any) {
    return { code: 500, status: false, message: `Database-Error`, data: error.message };
  }
}
  // public getMembersforAdmin = async (req: any) => {
  //   try {

  //     let limit = parseInt(req.query.limit) || 10;
  //     let offset = parseInt(req.query.offset) || 0;

  //     let result = await messageModel.aggregate()
  //       .match({})
  //       .group({
  //         _id: "$subOrderNumber",
  //         members: { $addToSet: "$sender" },
  //         lastMessage: { $last: "$message" }
  //       })
  //       .unwind("members")
  //       .lookup({
  //         from: "users",
  //         localField: "members",
  //         foreignField: "_id",
  //         as: "senders"
  //       })
  //       .unwind("senders")
  //       .sort({ "lastMessage.createdAt": -1 })
  //       .project({
  //         members: 1,
  //         lastMessage: 1
  //       })
  //       .skip(offset)
  //       .limit(limit)

  //     if (result.length > 0) {
  //       for (const item of result) {
  //         for (const member of item.members) {
  //           // Perform your async operation here
  //           if (member.profilePictureId) {
  //             const newId: any = await this.applicationService.getFile(req, member.profilePictureId.toString());
  //             member.profilePicture = newId.data.signedUrl; // Replace the original _id with the result of the async operation
  //           }
  //         }
  //       }
  //     }
  //     return { status: true, code: 200, message: `Data retrieved successfully`, data: result };

  //   } catch (error: any) {
  //     return { code: 500, status: false, message: `Database-Error`, data: error.message };
  //   }
  // }

  public adminOrderMember = async (req: any): Promise<any> => {
    try {

      let adminId = req.params.id;
      let orderNumber = req.query.orderNumber;
      let limit = req.query.limit || 10;
      let offset =  req.query.offset || 0;
      let search = await this.messageHelper.search(req.query)

        if (!connection.db) {
            throw new Error('Database connection is not established');
        }

        let orders: any = connection.db.collection('orders');
        let result = await orders.aggregate([
            {
                $match: { orderNumber: orderNumber }
            },
            {
                $unwind: "$sellerData"
            },
            {
                $lookup: {
                    from: 'messages',
                    localField: 'sellerData.subOrderNumber',
                    foreignField: 'subOrderNumber',
                    as: 'messages'
                }
            },
            {
                $match: { 'messages.0': { $exists: true } } // Filter to only include subOrderNumbers that have messages
            },
            {
                $lookup: {
                    from: 'users',
                    localField: 'sellerData.sellerId',
                    foreignField: '_id',
                    as: 'sellerInfo'
                }
            },
            {
                $unwind: "$sellerInfo"
            },
            {
                $addFields: {
                    lastMessage: { $arrayElemAt: ["$messages", -1] } // Get the last message
                }
            },
            {
                $project: {
                    subOrderNumber: "$sellerData.subOrderNumber",
                    lastMessage: {
                        message: "$lastMessage.message",
                        createdAt: "$lastMessage.createdAt"
                    },
                    user: {
                      displayName: "$sellerInfo.displayName",
                      userName: "$sellerInfo.userName",
                      profilePictureId: "$sellerInfo.profilePictureId"
                    },
                }
            },
            {
              $sort :{"lastMessage.createdAt" :-1}
            },
            { $match: { $or: search } },
            {
              $skip: offset
            },
            {
              $limit: limit
            }
        ]).toArray();

        if (result.length > 0) {
          for (const item of result) {
            if (item.user.profilePictureId) {
              const newId: any = await this.applicationService.getFile(req, item.user.profilePictureId.toString());
              item.user.profilePicture = newId.data.signedUrl; // Replace the original _id with the result of the async operation
            }

            let count
            // let inGroup = await this.isUserInGroup(item.subOrderNumber, adminId);
            
            // if(inGroup){
            //   count = 0
            // }else{
              count = await this.getPendingMessages(adminId, item.subOrderNumber, "admin");
              item["pendingMessages"] = count.data;
            // }

            
          }
        }
        return { status: true, code: 200, message: `Data retrieved successfully`, data: result };
    } catch (error: any) {
        return { code: 500, status: false, message: `Database-Error`, data: error.message };
    }
}
public updateMessageForMember = async (Id: string, subOrderNumber: string, type: string) => {
  try {
      let now = new Date();
      let updateResponse: any;

      if (!connection.db) {
          throw new Error('Database connection is not established');
      }
      let orderModel = connection.db.collection('orders');

      if (type === "user") {
          updateResponse = await orderModel.updateOne(
              {
                  userId: new Types.ObjectId(Id),
                  'sellerData.subOrderNumber': subOrderNumber
              },
              { $set: { 'sellerData.$.userOnline': now } }
          );
      } else if (type === "seller") {
          updateResponse = await orderModel.updateOne(
              {
                  'sellerData.sellerId': new Types.ObjectId(Id),
                  'sellerData.subOrderNumber': subOrderNumber
              },
              { $set: { 'sellerData.$.sellerOnline': now } }
          );
      } else if (type === "admin") {
          updateResponse = await orderModel.updateOne(
              {
                  'sellerData.subOrderNumber': subOrderNumber
              },
              { $set: { 'sellerData.$.adminOnline': now } }
          );
      }

      if (updateResponse.modifiedCount === 0) {
          return { status: false, code: 400, message: 'Failed to update online status' };
      }
      updateResponse["lastOnline"] = now;
      return { status: true, code: 200, message: `Data updated successfully`, data: updateResponse };

  } catch (error: any) {
      return { code: 500, status: false, message: `Database-Error`, data: error.message };
  }
}

public async getFitnessPendingMessageCount(userId: string, receiverId: string): Promise<any> {
  try {
    if (!userId || !receiverId) {
      return { code: 400, status: false, message: 'userId and receiverId are required', data: null };
    }

    const userModel: any = connection.db?.collection('users');

    // Find the chatReadStatus entry for this receiver
    const user = await userModel.findOne(
      { _id: new Types.ObjectId(userId), "chatReadStatus.partnerId": new Types.ObjectId(receiverId) },
      { projection: { "chatReadStatus.$": 1 } }
    );

    // If no chatReadStatus found → treat as first time (pendingCount = all messages)
    let fitnessLastSeen: Date | null = null;
    if (user && user.chatReadStatus && user.chatReadStatus.length > 0) {
      fitnessLastSeen = user.chatReadStatus[0].fitnessLastSeen;
    }

    // Query messages:
    // - sender = receiverId (partner sent messages)
    // - receiver = userId (messages to this user)
    // - createdAt > fitnessLastSeen (if exists)
    // - chatType = fitness
    const messageQuery: any = {
      sender: new Types.ObjectId(receiverId),
      receiverId: new Types.ObjectId(userId),
      chatType: 'fitness'
    };

    if (fitnessLastSeen) {
      messageQuery.createdAt = { $gt: new Date(fitnessLastSeen) };
    }

    const pendingCount = await messageModel.countDocuments(messageQuery);

    return {
      status: true,
      code: 200,
      message: 'Fitness pending message count retrieved successfully',
      data: {
        pendingCount,
        fitnessLastSeen
      }
    };

  } catch (error: any) {
    console.error('Error getting fitness pending message count:', error);
    return { code: 500, status: false, message: 'Database-Error', data: error.message };
  }
}


public getPendingMessages = async (Id: string, subOrderNumber: string, type: string): Promise<any> => {
  try {
      if (!connection.db) {
          throw new Error('Database connection is not established');
      }

      const orderModel = connection.db.collection('orders');
      let userOnline;

      if (type === "user") {
          // Fetch the userOnline timestamp from the orders collection
          const order = await orderModel.findOne(
              {
                  userId: new Types.ObjectId(Id),
                  'sellerData.subOrderNumber': subOrderNumber
              },
              {
                  projection: { 'sellerData.$': 1 }
              }
          );

          if (!order || !order.sellerData || !order.sellerData[0].userOnline) {
              return { status: false, code: 404, message: 'Order or userOnline timestamp not found' };
          }

          userOnline = order.sellerData[0].userOnline;

      } else if (type === "seller") {
          const order = await orderModel.findOne(
              {
                  'sellerData.sellerId': new Types.ObjectId(Id),
                  'sellerData.subOrderNumber': subOrderNumber
              },
              {
                  projection: { 'sellerData.$': 1 }
              }
          );

          if (!order || !order.sellerData || !order.sellerData[0].sellerOnline) {
              return { status: false, code: 404, message: 'Order or sellerOnline timestamp not found' };
          }

          userOnline = order.sellerData[0].sellerOnline;

      } else if (type === "admin") {
          const order = await orderModel.findOne(
              {
                  'sellerData.subOrderNumber': subOrderNumber
              },
              {
                  projection: { 'sellerData.$': 1 }
              }
          );

          if (!order || !order.sellerData || !order.sellerData[0].adminOnline) {
              return { status: false, code: 404, message: 'Order or adminOnline timestamp not found' };
          }

          userOnline = order.sellerData[0].adminOnline;
      }

      // Count the number of messages where createdAt is greater than userOnline
      let pendingMessagesCount = await messageModel.countDocuments({
          subOrderNumber: subOrderNumber,
          createdAt: { $gt: userOnline }
      });
      if (!pendingMessagesCount || pendingMessagesCount === 0) { pendingMessagesCount = 0 }
      return { status: true, code: 200, message: 'Pending messages count retrieved successfully', data: pendingMessagesCount };
  } catch (error: any) {
      return { code: 500, status: false, message: 'Database-Error', data: error.message };
  }
};

public getPendingMessagesForUserOrSeller = async (Id: string, type: string): Promise<any> => {
  try {
      if (!connection.db) {
          throw new Error('Database connection is not established');
      }

      const orderModel = connection.db.collection('orders');
      let orders;
      let pendingMessagesCount = 0;

      if (type === "user") {
          // Fetch all sellerData entries for the given userId
          orders = await orderModel.find(
              { userId: new Types.ObjectId(Id) },
              { projection: { sellerData: 1 } }
          ).toArray();

          for (const order of orders) {
              for (const sellerData of order.sellerData) {
                  if (sellerData.userOnline) {
                      // Count the number of messages where createdAt is greater than userOnline
                      const count = await messageModel.countDocuments({
                          subOrderNumber: sellerData.subOrderNumber,
                          createdAt: { $gt: sellerData.userOnline }
                      });
                      pendingMessagesCount += count;
                  }
              }
          }

      } else if (type === "seller") {
          // Fetch all sellerData entries for the given sellerId
          orders = await orderModel.find(
              { 'sellerData.sellerId': new Types.ObjectId(Id) },
              { projection: { sellerData: 1 } }
          ).toArray();

          for (const order of orders) {
              for (const sellerData of order.sellerData) {
                  if (sellerData.sellerOnline) {
                      // Count the number of messages where createdAt is greater than sellerOnline
                      const count = await messageModel.countDocuments({
                          subOrderNumber: sellerData.subOrderNumber,
                          createdAt: { $gt: sellerData.sellerOnline }
                      });
                      pendingMessagesCount += count;
                  }
              }
          }
      }

      return { status: true, code: 200, message: 'Pending messages count retrieved successfully', data: pendingMessagesCount };
  } catch (error: any) {
      return { code: 500, status: false, message: 'Database-Error', data: error.message };
  }
};
  public async findUserId(subOrderNumber: string, sellerId: string): Promise<string | null> {
    if (!connection.db) {
      throw new Error("Database connection is not established.");
    }
    let orderModel = connection.db.collection('orders');
    const order = await orderModel.findOne({
      'sellerData.subOrderNumber': subOrderNumber,
      'sellerData.sellerId': new Types.ObjectId(sellerId)
    }, { projection: { userId: 1 } });

    return order ? order.userId.toString() : null;
  }

  // Function to find sellerId given subOrderNumber and userId
  public async findSellerId(subOrderNumber: string, userId: string): Promise<string | null> {
    if (!connection.db) {
      throw new Error("Database connection is not established.");
    }
    let orderModel = connection.db.collection('orders');
    const order = await orderModel.findOne({
      userId: new Types.ObjectId(userId),
      'sellerData.subOrderNumber': subOrderNumber
    }, { projection: { 'sellerData.$': 1 } });

    return order && order.sellerData.length > 0 ? order.sellerData[0].sellerId.toString() : null;
  }

  public async getSellerAndUserId(subOrderNumber: string): Promise<{ sellerId: string, userId: string } | null> {
    try {
      if (!connection.db) {
        throw new Error('Database connection is not established');
      }
      let orderModel: any = connection.db.collection('orders');
      const order = await orderModel.findOne(
        { 'sellerData.subOrderNumber': subOrderNumber },
        { 'sellerData.$': 1, userId: 1 }
      );

      if (!order) {
        throw new Error('Order not found');
      }

      const sellerData = order.sellerData[0];
      const sellerId = sellerData.sellerId;
      const userId = order.userId;

      return { sellerId, userId };
    } catch (error) {
      console.error('Error retrieving sellerId and userId:', error);
      throw error;
    }
  }

  // Fitness-specific methods
  public async getFitnessConversation(req: any): Promise<any> {
    try {
      const { senderId, receiverId } = req;

      if (!senderId || !receiverId) {
        return { code: 400, status: false, message: 'senderId and receiverId are required', data: null };
      }

      // Generate conversation ID from sorted user IDs
      const conversationId = [senderId, receiverId].sort().join('_');

      const conversation = await messageModel.aggregate([
        {
          $match: {
            chatType: 'fitness',
            conversationId: conversationId
          }
        },
        {
          $lookup: {
            from: 'users',
            localField: 'sender',
            foreignField: '_id',
            as: 'sender'
          }
        },
        {
          $facet: {
            paginatedResults: [
              { $sort: { "createdAt": -1 } },
              {
                $project: {
                  "_id": 1,
                  "message": 1,
                  "image": 1,
                  "displayName": { $arrayElemAt: ["$sender.displayName", 0] },
                  "firstName": { $arrayElemAt: ["$sender.firstName", 0] },
                  "createdAt": 1,
                  "isSender": { $eq: [{ $arrayElemAt: ["$sender._id", 0] }, new Types.ObjectId(senderId)] }
                }
              }
            ],
            totalCount: [
              { $count: "count" }
            ]
          }
        }
      ]);

      // if (conversation[0].paginatedResults.length > 0) {
      //   for (let i = 0; i < conversation[0].paginatedResults.length; i++) {
      //     if (conversation[0].paginatedResults[i].image) {
      //       conversation[0].paginatedResults[i].image.signedUrl = await this.getSignedUrl(conversation[0].paginatedResults[i].image.image);
      //     }
      //   }
      // }

      const totalData = {
        conversation: conversation[0].paginatedResults || [],
        fetchCount: conversation[0].paginatedResults.length || 0,
        total: conversation[0].totalCount[0] ? conversation[0].totalCount[0].count : 0
      };

      return { status: true, code: 200, message: 'Data retrieved successfully', data: totalData };
    } catch (error: any) {
      return { code: 500, status: false, message: 'Database-Error', data: error.message };
    }
  }

public async getFitnessMembers(userId: string, userType: string): Promise<any> {
  try {
    if (!userId) {
      return { code: 400, status: false, message: 'userId is required', data: null };
    }

    if (!userType) {
      return { code: 400, status: false, message: 'userType is required', data: null };
    }

    const userModel: any = connection.db?.collection('users');

    // Get all unique conversation partners filtered by userType
    const conversations = await messageModel.aggregate([
      {
        $match: {
          chatType: 'fitness',
          $or: [
            { sender: new Types.ObjectId(userId) },
            { receiverId: new Types.ObjectId(userId) }
          ]
        }
      },
      {
        $addFields: {
          partnerId: {
            $cond: {
              if: { $eq: ["$sender", new Types.ObjectId(userId)] },
              then: "$receiverId",
              else: "$sender"
            }
          }
        }
      },
      {
        $group: {
          _id: "$partnerId",
          lastMessage: { $last: "$message" },
          lastMessageTime: { $last: "$createdAt" }
        }
      },
      {
        $lookup: {
          from: 'users',
          let: { partnerId: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$_id", "$$partnerId"] },
                    { $eq: ["$userType", userType] }
                  ]
                }
              }
            }
          ],
          as: 'userData'
        }
      },
      {
        $match: {
          "userData.0": { $exists: true } // Only include documents where userData array is not empty
        }
      },
      {
        $unwind: "$userData"
      },
      {
        $project: {
          _id: 0,
          user: {
            _id: "$userData._id",
            displayName: "$userData.displayName",
            firstName: "$userData.firstName",
            lastName: "$userData.lastName",
            profilePictureId: "$userData.profilePictureId",
            userType: "$userData.userType" // Include userType in response
          },
          lastMessage: {
            message: "$lastMessage",
            createdAt: "$lastMessageTime"
          }
        }
      },
      { $sort: { "lastMessage.createdAt": -1 } }
    ]);
        if (conversations.length > 0) {
          for (const item of conversations) {
            if (item.user.profilePictureId) {
              let req: any = {};
              const newId: any = await this.applicationService.getFile(req, item.user.profilePictureId.toString());
              item.user.profilePicture = newId.data.signedUrl; // Replace the original _id with the result of the async operation
            }

            let count

              count = await this.getFitnessPendingMessageCount(item.user._id.toString(),userId.toString());
              item["pendingMessages"] = count.data.pendingCount || 0;

            
          }
        }
    return { status: true, code: 200, message: 'Data retrieved successfully', data: conversations };
  } catch (error: any) {
    return { code: 500, status: false, message: 'Database-Error', data: error.message };
  }
}
  public async updateFitnessLastSeen(userId: string, receiverId: string): Promise<any> {
  try {
    if (!userId || !receiverId) {
      return { code: 400, status: false, message: 'userId and receiverId are required', data: null };
    }

    const userModel: any = connection.db?.collection('users');

    // Check if the entry for this partner already exists
    const user = await userModel.findOne(
      { _id: new Types.ObjectId(userId), "chatReadStatus.partnerId": new Types.ObjectId(receiverId) },
      { projection: { "chatReadStatus.$": 1 } }
    );

    if (user) {
      // Update existing fitnessLastSeen
      await userModel.updateOne(
        { _id: new Types.ObjectId(userId), "chatReadStatus.partnerId": new Types.ObjectId(receiverId) },
        { $set: { "chatReadStatus.$.fitnessLastSeen": new Date() } }
      );

      return {
        status: true,
        code: 200,
        message: 'Fitness last seen updated successfully',
        data: { partnerId: receiverId, fitnessLastSeen: new Date() }
      };

    } else {
      // If no entry exists → push a new one
      await userModel.updateOne(
        { _id: new Types.ObjectId(userId) },
        {
          $push: {
            chatReadStatus: {
              partnerId: new Types.ObjectId(receiverId),
              fitnessLastSeen: new Date()
            }
          }
        }
      );

      return {
        status: true,
        code: 200,
        message: 'Fitness last seen created successfully',
        data: { partnerId: receiverId, fitnessLastSeen: new Date() }
      };
    }

  } catch (error: any) {
    console.error('Error getting/updating fitness last seen:', error);
    return { code: 500, status: false, message: 'Database-Error', data: error.message };
  }
}

  // Optional: Get fitness last seen for displaying in chat
  public async getFitnessLastSeen(userId: string): Promise<any> {
    try {
      if (!userId) {
        return { code: 400, status: false, message: 'userId is required', data: null };
      }

      const userModel: any = connection.db?.collection('users');
      const user = await userModel.findOne(
        { _id: new Types.ObjectId(userId) },
        { projection: { fitnessLastSeen: 1, displayName: 1, firstName: 1, lastName: 1 } }
      );

      if (user) {
        return { status: true, code: 200, message: 'Fitness last seen retrieved successfully', data: user };
      } else {
        return { status: false, code: 404, message: 'User not found', data: null };
      }
    } catch (error: any) {
      console.error('Error getting fitness last seen:', error);
      return { code: 500, status: false, message: 'Database-Error', data: error.message };
    }
  }

  public async sendFitnessMessage(senderId: string, receiverId: string, message: string): Promise<any> {
    try {
      if (!senderId || !receiverId || !message) {
        return { code: 400, status: false, message: 'senderId, receiverId, and message are required', data: null };
      }

      // Generate conversation ID from sorted user IDs
      const conversationId = [senderId, receiverId].sort().join('_');

      const messageBody = {
        chatType: 'fitness',
        conversationId: conversationId,
        sender: new Types.ObjectId(senderId),
        receiverId: new Types.ObjectId(receiverId),
        message: message,
        image: {},
        createdAt: new Date()
      };

      const newMessage = new messageModel(messageBody);
      const result = await newMessage.save();

      if (result) {
        // Emit real-time updates
        const io = this.SocketHelper.io;
        const socketId = await this.SocketHelper.getReceiverSocketId(receiverId);
        if (socketId) {
        io.to(socketId).emit('receiveMessage', result);

        }
        // Send to receiver's personal room
        const senderSocketId = await this.SocketHelper.getReceiverSocketId(senderId);
        if (senderSocketId) {
        io.to(senderSocketId).emit('receiveMessage', result);

        }
        // Send to receiver's personal room
        // Send to conversation room if both users are in it
        // io.to(conversationId).emit('receiveMessage', result);

        return { status: true, code: 200, message: 'Message sent successfully', data: result };
      }

      return { status: false, code: 409, message: 'Failed to send message', data: null };
    } catch (error: any) {
      return { code: 500, status: false, message: 'Database-Error', data: error.message };
    }
  }

}