import * as express from 'express';
import Logger from '../helpers/logger';
import * as appRootPath from 'app-root-path';
import { Server } from 'socket.io';
import * as http from 'http';
import SocketHelper from './socketHelper';
import messageDal from '../dal/messageDal';

declare global {
  namespace NodeJS {
    interface Global {
      logger: any;
      rootPath: any;
      [key: string]: any;
    }
  }
}

class App {
  public app: any;
  public port: number;
  public socket: any;
  public userSocketMap: any;
  public socketHelper: SocketHelper;
  public messageDal: messageDal;

  constructor(appInit: { port: number; defaults: any; middleWares: any; routes: any; socketHelper: SocketHelper }) {
    this.setGlobals();
    global.logger.info('Application Initialization Started');
    global.logger.info('port:', appInit.port);
    this.socketHelper = appInit.socketHelper; // Use the passed socketHelper instance
    this.messageDal = new messageDal(this.socketHelper); // Pass the socketHelper instance to messageDal
    this.socketHelper.setMessageDal(this.messageDal); // Set the messageDal instance in SocketHelper
    this.app = this.socketHelper.theApp();
    this.port = appInit.port;
    this.userSocketMap = {}; // {userId: socketId}
    this.setDefaults(appInit.defaults);
    this.middlewares(appInit.middleWares);
    this.routes(appInit.routes);
    this.assets();
    this.template();
    this.socketHelper.socketSetup();
  }

  private setGlobals() {
    global.logger = new Logger().getLogger();
    global.rootPath = appRootPath;
  }

  private setDefaults(middleWares: { forEach: (arg0: (middleWare: any) => void) => void }) {
    global.logger.info('Application Setting Defaults');
    middleWares.forEach((middleWare) => {
      this.app.use(middleWare);
    });
  }

  private middlewares(middleWares: { forEach: (arg0: (middleWare: any) => void) => void }) {
    global.logger.info('Application Setting Middlewares');
    middleWares.forEach((middleWare) => {
      this.app.use(middleWare);
    });
  }

  private routes(controllers: { forEach: (arg0: (controller: any) => void) => void }) {
    global.logger.info('Application Setting Routes');
    controllers.forEach((controller) => {
      this.app.use('/', controller.router);
    });
  }

  private assets() {
    this.app.use(express.static('public'));
    this.app.use(express.static('views'));
  }

  private template() {
    this.app.set('view engine', 'jade');
  }

  public listen() {
    let httpServer = this.socketHelper.theHttpServer();
    httpServer.listen(this.port, '0.0.0.0', () => {
      global.logger.info(`Application listening on the port :${this.port}`);
    });
  }
}

export default App;