import { Server } from 'socket.io';
import * as http from 'http';
import * as express from 'express';
import messageDal from '../dal/messageDal';

class SocketHelper {
  public app: express.Application;
  public io: Server;
  private httpServer: http.Server;
  public userSocketMap: any;
  private static instance: SocketHelper;
  private messageDal: messageDal;
  private isSetupComplete: boolean = false; // Add a flag to check if setup is complete

  private constructor(messageDal: messageDal) {
    this.app = express();
    this.httpServer = http.createServer(this.app);
    this.userSocketMap = {}; // {userId: socketId}
    this.io = new Server(this.httpServer, {
      cors: {
        origin: '*', // Update as needed for security
        methods: ['GET', 'POST'],
      },
      path: '/chat/socket.io', // Custom socket path
    });
    this.messageDal = messageDal;
  }

  public static getInstance(messageDal: any | null): SocketHelper {
    if (!SocketHelper.instance) {
      SocketHelper.instance = new SocketHelper(messageDal);
    }
    return SocketHelper.instance;
  }

  public setMessageDal(messageDal: messageDal) {
    this.messageDal = messageDal;
  }

  public theApp = () => {
    return this.app;
  }

  public theHttpServer = () => {
    return this.httpServer;
  }

  public getReceiverSocketId = (senderId: any) => {
    return this.userSocketMap[senderId];
  };

  public getUserIdBySocketId(socketId: string): string | null {
    for (const [userId, id] of Object.entries(this.userSocketMap)) {
      if (id === socketId) {
        return userId;
      }
    }
    return null;
  }

  private async handleFitnessReadMessages(socket: any, data: any, userId: string) {
    // Fitness: Direct chat between two users
    const req = {
      senderId: userId,
      receiverId: data.receiverId,
      limit: data.limit,
      offset: data.offset
    };
    const messages = await this.messageDal.getFitnessConversation(req);
    socket.emit('readMessages', messages.data);
  }

  private async handleBatayeqReadMessages(socket: any, data: any, userId: string) {
    // Existing Batayeq logic (unchanged)
    const req = {
      params: { id: data.subOrderNumber },
      query: { limit: data.limit, offset: data.offset, userId: userId },
    };
    const updations = await this.messageDal.getConversation(req);
    socket.emit('readMessages', updations.data);
  }

  private async handleFitnessGetMembers(socket: any, data: any, userId: string) {
    // Fitness: Get all users who have chatted with this user\
      if (!data.userType) {
        socket.emit('error', 'userType is undefined');
        return;
      }
    const members = await this.messageDal.getFitnessMembers(userId,data.userType);
    socket.emit('membersData', members.data);
  }

  private async handleBatayeqGetMembers(socket: any, data: any, userId: string) {
    // Existing Batayeq logic (unchanged)
    const req = {
      params: { id: userId },
      query: {
        limit: data?.limit,
        offset: data?.offset,
        type: data.type,
        orderNumber: data?.orderNumber,
        search: data?.search,
      },
      headers: {
        userid: socket.handshake.query.userId,
        authorization: socket.handshake.query.token,
      },
    };

    let members: any;
    if (data.type === 'user') {
      members = await this.messageDal.getMembersforUser(req);
      if (!members.status) {
        socket.emit('error', 'error is occured while getting members');
        return;
      }
    }
    if (data.type === 'seller') {
      members = await this.messageDal.getMembersforSeller(req);
      if (!members.status) {
        socket.emit('error', 'error is occured while getting members');
        return;
      }
    }
    if (data.type === 'admin') {
      members = await this.messageDal.adminOrderMember(req);
      if (!members.status) {
        socket.emit('error', 'error is occured while getting members');
        return;
      }
    }
    socket.emit('membersData', members.data);
  }

  private async handleFitnessSendMessage(socket: any, data: any, userId: string) {
    const io = this.io;
    // Fitness: Send to specific user's socket
    const message = await this.messageDal.sendFitnessMessage(
      userId,
      data.receiverId,
      data.message
    );

    const receiverSocketId = this.getReceiverSocketId(data.receiverId);
    if (receiverSocketId) {
      io.to(receiverSocketId).emit('newMessage', message);
    }
    socket.emit('messageSent', message);
  }

  private async handleBatayeqSendMessage(socket: any, data: any, userId: string) {
    // Existing Batayeq group message logic
    // Send to room (subOrderNumber)
    socket.to(data.subOrderNumber).emit('newMessage', data);
  }

  public socketSetup() {
    if (this.isSetupComplete) return this.io; // Return if setup is already complete

    const io = this.io;

    io.on('connection', (socket) => {
      const userId: any = socket.handshake.query.userId;
      const chatType: string = socket.handshake.query.chatType as string || 'batayeq'; // Default to batayeq

      if (!userId) {
        socket.emit('error', 'userId is required');
        return;
      }

      // Store chat type in socket for context
      (socket as any).chatType = chatType;

      console.log(`user connected with id: ${userId}, chatType: ${chatType} and socket id: ${socket.id}`);
      global.logger.info(`user connected with id: ${userId}, chatType: ${chatType} and socket id: ${socket.id}`);
      if (userId != 'undefined') this.userSocketMap[userId] = socket.id;
      io.emit('getOnlineUsers', Object.keys(this.userSocketMap));
      io.emit('onlineStatus', { userId: userId, status: true });

      socket.on('joinGroup', async (data) => {
        if (!data.subOrderNumber && !userId) {
          socket.emit('error', 'subOrderNumber is required');
          return;
        }

        console.log('an user joined by id', userId, " with data :", data);

        socket.join(data.subOrderNumber);

        let lastOnline = await this.messageDal.updateMessageForMember(userId, data.subOrderNumber, data.type);
        if (!lastOnline.status) {
          socket.emit('error', 'error is occured while updating last online');
          return;
        }
        socket.to(data.subOrderNumber).emit('userJoined', { message: `${data.userId} has joined the group.` });
      });

      socket.on('leaveGroup', async (data) => {
        if (!data.subOrderNumber) {
          socket.emit('error', 'subOrderNumber is required');
          return;
        }
        let lastOnline = await this.messageDal.updateMessageForMember(userId, data.subOrderNumber, data.type);
        if (!lastOnline.status) {
          socket.emit('error', 'error is occured while updating last online');
          return;
        }
        socket.leave(data.subOrderNumber);
      });

      socket.on('getPendingMessages', async (data) => {
        const { userId, subOrderNumber, type } = data;
        const pending = await this.messageDal.getPendingMessages(userId, subOrderNumber, type);

        const receiverSocketId = this.getReceiverSocketId(userId);
        if (receiverSocketId) {
          io.to(receiverSocketId).emit('pendingMessagesUpdated', { subOrderNumber, pendingMessages: pending.data });
        }
      });

      socket.on('displayChat', async (data) => {
        if (!data.subOrderNumber) {
          socket.emit('error', 'subOrderNumber is required');
          return;
        }

        if (!data.type) {
          socket.emit('error', 'type is required');
          return;
        }

        let result;

        if (data.type === 'user') {
          const sellerId: any = await this.messageDal.findSellerId(data.subOrderNumber, userId);
          const userData: any = await this.messageDal.findUser(sellerId);
          let active = Object.keys(this.userSocketMap).includes(sellerId);
          result = {
            sellerId: sellerId,
            userId: userId,
            userData,
            subOrderNumber: data.subOrderNumber,
            onlineStatus: active,
          };
        } else if (data.type === 'seller') {
          const userid: any = await this.messageDal.findUserId(data.subOrderNumber, userId);
          const userData: any = await this.messageDal.findUser(userid);
          let active = Object.keys(this.userSocketMap).includes(userid);
          result = {
            sellerId: userId,
            userId: userid,
            userData,
            subOrderNumber: data.subOrderNumber,
            onlineStatus: active,
          };
        } else {
          const usersData: any = await this.messageDal.getSellerAndUserId(data.subOrderNumber);
          result = {
            sellerId: usersData.userId,
            userId: usersData.sellerId,
            subOrderNumber: data.subOrderNumber,
          };
        }

        socket.emit('chat', result);
      });

      socket.on('allCount', async (data) => {
        if (!data.type) {
          socket.emit('error', 'type is required');
          return;
        }

        const count: any = await this.messageDal.getPendingMessagesForUserOrSeller(userId, data.type);

        socket.emit('chatCount', count);
      });

      socket.on('readMessages', async (data) => {
        const socketChatType = (socket as any).chatType;

        if (socketChatType === 'fitness') {
          await this.handleFitnessReadMessages(socket, data, userId);
        } else {
          await this.handleBatayeqReadMessages(socket, data, userId);
        }
      });

      socket.on('readNotification', async (data) => {
        const req = {
          params: { id: userId },
          query: { limit: data.limit, offset: data.offset },
        };
        const result = await this.messageDal.getNotificationsByUserId(req);
        socket.emit('readNotification', result.data);
      });

      socket.on('allNotification', async (data) => {
        const req = {
          params: { id: userId },
        };
        const result = await this.messageDal.getUnreadNotificationsCount(req);
        socket.emit('notificationCount', result.data);
      });

      socket.on('moveNotification', async (data) => {
        const req = {
          params: { userid: userId },
        };
        const result = await this.messageDal.updateUserOnline(req);
        socket.emit('moveNotification', result.data);
      });

      socket.on('getMembers', async (data) => {
        const socketChatType = (socket as any).chatType;

        if (socketChatType === 'fitness') {
          await this.handleFitnessGetMembers(socket, data, userId);
        } else {
          await this.handleBatayeqGetMembers(socket, data, userId);
        }
      });

      socket.on('sendMessage', async (data) => {
        const socketChatType = (socket as any).chatType;

        if (socketChatType === 'fitness') {
          await this.handleFitnessSendMessage(socket, data, userId);
        } else {
          await this.handleBatayeqSendMessage(socket, data, userId);
        }
      });


     socket.on('leaveChat', async (data) => {

      let receiverId = data.receiverId;
        const socketChatType = (socket as any).chatType;
        if (socketChatType === 'fitness') {
          // Update fitness last seen in user collection
          this.messageDal.updateFitnessLastSeen(userId,receiverId).then(() => {
            console.log(`Updated fitness last seen for user: ${userId}`);
          }).catch((error) => {
            console.error(`Error updating fitness last seen for user ${userId}:`, error);
          });

          // Notify fitness contacts that user is offline
          io.emit('fitnessUserOffline', { userId, timestamp: new Date() });

        } else {
          // For Batayeq, last seen is handled in leaveGroup or specific order contexts
          // No automatic last seen update on disconnect for Batayeq
        }
      });

      socket.on('disconnect', () => {
        console.log('user disconnected with userid:', userId, 'from socketId:', socket.id);
        const socketChatType = (socket as any).chatType;

        // Common disconnect logic (unchanged)
        delete this.userSocketMap[userId];
        io.emit('getOnlineUsers', Object.keys(this.userSocketMap));
        io.emit('onlineStatus', { userId: userId, status: false });
      });
    });

    this.isSetupComplete = true; // Mark setup as complete
    return io;
  }
}

export default SocketHelper;